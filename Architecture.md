# Assistant-Go 架構設計文檔

## 專案概述與設計理念

這份文檔詳細說明了 Assistant-Go 專案的架構設計，旨在幫助開發者和 AI 編碼助理深入理解專案結構。我們遵循 Go 語言的慣例和最佳實踐，建立一個模組化、可測試且易於擴展的應用程式。

在開始之前，讓我們先理解幾個核心設計原則。首先，這個專案採用了**領域驅動設計 (DDD)** 的思想，將不同的功能區域清晰地分離。其次，我們遵循 **介面隔離原則**，確保每個模組都可以獨立開發和測試。最後，整個架構設計都圍繞著**可擴展性**，讓未來的 AI 整合能夠順利進行。

## 專案結構總覽

讓我們從整體的資料夾結構開始，逐步深入到每個部分的細節：

```
assistant-go/
├── cmd/                     # 應用程式進入點
├── internal/                # 私有應用程式程式碼
├── pkg/                     # 可重用的公共函式庫
├── api/                     # API 定義和協議
├── configs/                 # 配置檔案
├── deployments/             # 部署相關檔案
├── docs/                    # 文檔
├── scripts/                 # 建構和維護腳本
├── test/                    # 額外的測試檔案
├── web/                     # Web 資源（如果需要）
├── go.mod                   # Go 模組定義
├── go.sum                   # Go 模組校驗和
├── Makefile                 # 建構自動化
├── Taskfile.yml             # Task 執行定義
├── README.md                # 專案說明
└── .gitignore               # Git 忽略規則
```

## 詳細的目錄結構和設計說明

### `/cmd` - 應用程式進入點

這個目錄包含了應用程式的主要進入點。根據 Go 的慣例，每個可執行程式都應該有自己的子目錄。

```
cmd/
└── assistant/
    └── main.go              # 主程式進入點
```

main.go 的設計應該保持簡潔，只負責初始化和啟動應用程式：

```go
// main.go - 保持簡潔，只處理初始化邏輯
package main

import (
    "log"
    "github.com/koopa/assistant-go/internal/app"
    "github.com/koopa/assistant-go/internal/config"
)

func main() {
    // 載入配置
    cfg, err := config.Load()
    if err != nil {
        log.Fatal("Failed to load config:", err)
    }
    
    // 建立並執行應用程式
    application := app.New(cfg)
    if err := application.Run(); err != nil {
        log.Fatal("Application error:", err)
    }
}
```

### `/internal` - 私有應用程式程式碼

這是專案的核心，包含所有不應被外部匯入的程式碼。Go 1.4 之後，`internal` 目錄有特殊含義：其中的程式碼只能被同一專案的其他程式碼匯入。

```
internal/
├── app/                     # 應用程式核心邏輯
│   ├── app.go              # 應用程式主結構
│   ├── lifecycle.go         # 生命週期管理
│   └── events.go            # 事件系統
├── config/                  # 配置管理
│   ├── config.go            # 配置結構定義
│   ├── loader.go            # 配置載入器
│   └── validator.go         # 配置驗證
├── modules/                 # 功能模組
│   ├── interfaces.go        # 模組介面定義
│   ├── k8s/                # Kubernetes 管理模組
│   │   ├── manager.go       # K8s 管理器實作
│   │   ├── client.go        # K8s 客戶端包裝
│   │   ├── resources.go     # 資源管理
│   │   └── ui.go           # UI 元件
│   ├── database/           # 資料庫管理模組
│   │   ├── manager.go      # 資料庫管理器
│   │   ├── postgres.go     # PostgreSQL 特定邏輯
│   │   ├── query.go        # 查詢建構器
│   │   └── ui.go          # UI 元件
│   ├── taskrunner/         # 任務執行模組
│   │   ├── runner.go       # 任務執行器
│   │   ├── parser.go       # Makefile/Taskfile 解析器
│   │   ├── executor.go     # 命令執行
│   │   └── ui.go          # UI 元件
│   ├── mcp/                # MCP 視覺化模組
│   │   ├── visualizer.go   # MCP 視覺化器
│   │   ├── protocol.go     # 協議處理
│   │   └── ui.go          # UI 元件
│   └── ai/                 # AI 助理模組
│       ├── assistant.go    # AI 助理核心
│       ├── providers/      # AI 提供者
│       │   ├── claude.go   # Claude 整合
│       │   └── gemini.go   # Gemini 整合
│       ├── langchain.go    # LangChain 整合
│       └── ui.go          # UI 元件
├── ui/                     # 共用 UI 元件
│   ├── theme.go           # 主題系統
│   ├── widgets/           # 自定義小部件
│   │   ├── terminal.go    # 終端機風格元件
│   │   ├── chart.go       # 圖表元件
│   │   └── statusbar.go   # 狀態列
│   └── layouts/           # 版面配置
│       └── cyber.go       # 駭客風格版面
└── utils/                 # 內部工具函式
    ├── logger.go          # 日誌系統
    ├── errors.go          # 錯誤處理
    └── crypto.go          # 加密工具
```

### `/pkg` - 可重用的公共函式庫

這個目錄包含可以被外部專案使用的程式碼。這些應該是通用的、與應用程式邏輯無關的工具。

```
pkg/
├── terminal/              # 終端機相關工具
│   ├── colors.go         # 顏色處理
│   └── effects.go        # 特效（如閃爍文字）
├── monitoring/           # 監控工具
│   ├── metrics.go       # 指標收集
│   └── health.go        # 健康檢查
└── validation/          # 通用驗證工具
    └── validators.go    # 驗證函式
```

### `/api` - API 定義和協議

即使這是一個 GUI 應用程式，我們仍然需要定義清晰的內部 API 來支援模組間通訊和未來的擴展。

```
api/
├── proto/                # Protocol Buffers 定義（如果使用）
├── openapi/             # OpenAPI 規範（用於 REST API）
└── events/              # 事件定義
    ├── types.go         # 事件類型
    └── handlers.go      # 事件處理器介面
```

### 模組介面設計

每個模組都應該實作統一的介面，這樣可以確保一致性和可測試性：

```go
// internal/modules/interfaces.go
package modules

import (
    "context"
    "fyne.io/fyne/v2"
)

// Module 定義了所有功能模組必須實作的介面
type Module interface {
    // 初始化模組
    Initialize(ctx context.Context) error
    
    // 返回模組的 UI 內容
    Content() fyne.CanvasObject
    
    // 重新整理模組資料
    Refresh() error
    
    // 關閉模組並清理資源
    Shutdown() error
    
    // 返回模組資訊
    Info() ModuleInfo
}

// ModuleInfo 包含模組的元資料
type ModuleInfo struct {
    ID          string
    Name        string
    Description string
    Version     string
    Author      string
}

// EventHandler 定義事件處理介面
type EventHandler interface {
    // 處理事件
    HandleEvent(event Event) error
    
    // 訂閱特定類型的事件
    Subscribe(eventType string) error
    
    // 取消訂閱
    Unsubscribe(eventType string) error
}
```

### 配置管理最佳實踐

配置系統應該支援多層次的配置來源，並且能夠處理環境變數：

```go
// internal/config/config.go
package config

import (
    "time"
)

// Config 定義應用程式的完整配置結構
type Config struct {
    App        AppConfig        `yaml:"app"`
    Modules    ModulesConfig    `yaml:"modules"`
    UI         UIConfig         `yaml:"ui"`
    Security   SecurityConfig   `yaml:"security"`
}

// AppConfig 應用程式基本配置
type AppConfig struct {
    Name        string        `yaml:"name"`
    Version     string        `yaml:"version"`
    LogLevel    string        `yaml:"log_level"`
    Environment string        `yaml:"environment"`
}

// ModulesConfig 各模組的配置
type ModulesConfig struct {
    K8s        K8sConfig        `yaml:"k8s"`
    Database   DatabaseConfig   `yaml:"database"`
    AI         AIConfig         `yaml:"ai"`
    TaskRunner TaskRunnerConfig `yaml:"task_runner"`
}

// 配置載入優先順序：
// 1. 預設值
// 2. 配置檔案
// 3. 環境變數
// 4. 命令列參數
```

### 錯誤處理策略

Go 的錯誤處理需要特別注意。我們採用錯誤包裝的方式來保留錯誤堆疊：

```go
// internal/utils/errors.go
package utils

import (
    "fmt"
)

// ErrorCode 定義錯誤代碼
type ErrorCode string

const (
    ErrCodeInternal     ErrorCode = "INTERNAL"
    ErrCodeConfig       ErrorCode = "CONFIG"
    ErrCodeModule       ErrorCode = "MODULE"
    ErrCodeNetwork      ErrorCode = "NETWORK"
    ErrCodeAuth         ErrorCode = "AUTH"
)

// AppError 自定義錯誤類型
type AppError struct {
    Code    ErrorCode
    Message string
    Cause   error
}

func (e *AppError) Error() string {
    if e.Cause != nil {
        return fmt.Sprintf("[%s] %s: %v", e.Code, e.Message, e.Cause)
    }
    return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// Wrap 包裝錯誤並添加上下文
func WrapError(err error, code ErrorCode, message string) error {
    return &AppError{
        Code:    code,
        Message: message,
        Cause:   err,
    }
}
```

### 測試策略

每個模組都應該有對應的測試檔案，遵循 Go 的測試慣例：

```
internal/
├── modules/
│   ├── k8s/
│   │   ├── manager.go
│   │   ├── manager_test.go      # 單元測試
│   │   └── manager_integration_test.go  # 整合測試
```

測試應該包含表格驅動測試的模式：

```go
// manager_test.go
func TestK8sManager_ListPods(t *testing.T) {
    tests := []struct {
        name      string
        namespace string
        want      int
        wantErr   bool
    }{
        {
            name:      "list pods in default namespace",
            namespace: "default",
            want:      3,
            wantErr:   false,
        },
        // 更多測試案例...
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // 測試邏輯
        })
    }
}
```

### 依賴注入和解耦

為了保持程式碼的可測試性和靈活性，我們使用依賴注入模式：

```go
// internal/app/app.go
package app

// Application 主應用程式結構
type Application struct {
    config     *config.Config
    modules    map[string]modules.Module
    eventBus   *EventBus
    logger     *Logger
}

// New 使用依賴注入建立應用程式
func New(
    cfg *config.Config,
    logger *Logger,
    eventBus *EventBus,
) *Application {
    return &Application{
        config:   cfg,
        modules:  make(map[string]modules.Module),
        eventBus: eventBus,
        logger:   logger,
    }
}
```

## 開發指南和最佳實踐

### 命名慣例

Go 有明確的命名慣例，我們應該嚴格遵循：
- 套件名稱使用小寫，不使用底線或駝峰命名
- 匯出的名稱（公開的）以大寫字母開頭
- 介面名稱通常以 "er" 結尾（如 Reader, Writer）
- 縮寫保持一致的大小寫（如 URL, HTTP, ID）

### 並發處理

由於這是一個 GUI 應用程式，我們需要特別注意並發處理。所有長時間運行的操作都應該在後台 goroutine 中執行：

```go
// 正確的方式：使用 goroutine 處理耗時操作
func (m *K8sManager) RefreshPods() {
    go func() {
        pods, err := m.client.ListPods()
        if err != nil {
            m.logger.Error("Failed to list pods", err)
            return
        }
        
        // 使用 Fyne 的執行緒安全方法更新 UI
        m.app.Settings().SetTheme(theme)
    }()
}
```

### 資源管理

確保所有資源都正確釋放，特別是在模組關閉時：

```go
func (m *DatabaseManager) Shutdown() error {
    // 關閉所有資料庫連接
    for _, conn := range m.connections {
        if err := conn.Close(); err != nil {
            m.logger.Warn("Failed to close connection", err)
        }
    }
    
    // 清理其他資源
    close(m.queryChannel)
    
    return nil
}
```

## 建構和部署

專案使用 Makefile 來自動化常見任務：

```makefile
# Makefile
.PHONY: build test clean

build:
	go build -o bin/assistant ./cmd/assistant

test:
	go test -v ./...

clean:
	rm -rf bin/

run:
	go run ./cmd/assistant

lint:
	golangci-lint run

fmt:
	go fmt ./...
```

## 結語

這個架構設計考慮了 Go 語言的特性和最佳實踐，同時保持了足夠的彈性來支援未來的擴展。記住，好的架構是演進出來的，而不是一開始就設計完美的。隨著專案的發展，我們可以根據實際需求調整和優化這個架構。

最重要的是，這個設計讓任何開發者（包括 AI 助理）都能快速理解專案結構，並且能夠獨立開發和測試各個模組。每個部分都有明確的職責，遵循單一職責原則，這讓程式碼更容易維護和擴展。